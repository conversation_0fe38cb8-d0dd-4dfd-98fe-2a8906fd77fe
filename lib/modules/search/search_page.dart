import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/detail_tab/detail_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<StatefulWidget> createState() => _SearchState();
}

class _SearchState extends State<SearchPage> {
  final TextEditingController _keywordController = TextEditingController();

  final List<TransactionItem> _dataList = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Container(
                        margin: EdgeInsets.only(right: 12),
                        padding: EdgeInsets.only(right: 6),
                        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(18)),
                        height: 35,
                        child: Row(
                          children: [
                            Expanded(
                              child: Container(
                                  margin: EdgeInsets.only(left: 4, right: 4),
                                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                                  decoration: BoxDecoration(color: MColor.xFFF4FEFA, borderRadius: BorderRadius.circular(15)),
                                  child: TextField(
                                    style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF577F8C),
                                    controller: _keywordController,
                                    keyboardType: TextInputType.text,
                                    textInputAction: TextInputAction.search,
                                    onSubmitted: (value) {
                                      _doSearch();
                                    },
                                    onChanged: (value) {
                                      setState(() {});
                                    },
                                    decoration: InputDecoration(
                                      hintStyle: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF577F8C),
                                      border: OutlineInputBorder(borderSide: BorderSide.none),
                                      focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                      enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
                                      filled: true,
                                      fillColor: Colors.transparent,
                                      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                                      isDense: true,
                                      hintText: '输入你要搜索的关键词',
                                    ),
                                  )),
                            ),
                            GestureDetector(
                              onTap: () {
                                _doSearch();
                              },
                              child: Image.asset(
                                'assets/images/ic_search.png',
                                width: 18,
                                height: 18,
                                fit: BoxFit.fill,
                              ),
                            ),
                          ],
                        ),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: GetBuilder<UserStore>(
                      id: 'search_history',
                      builder: (context) {
                        if (_dataList.isEmpty) {
                          return _noDataView;
                        }
                        return Column(
                          children: [
                            _headerView,
                            Expanded(
                              child: ListView.separated(
                                  padding: EdgeInsets.zero,
                                  itemBuilder: (context, index) {
                                    return Container(
                                        color: MColor.xFFF5F5F5,
                                        child: TransactionItemView(_dataList[index], headerView: null, actions: [
                                          TransactionItemAction(
                                            label: '删除',
                                            icon: Icons.delete,
                                            bgColor: MColor.xFFFF7858,
                                            fgColor: MColor.xFFFFFFFF,
                                            onPressed: () {
                                              showCustomDialog(
                                                '确认删除',
                                                content: '删除后不可恢复，请确认是否删除',
                                                cancel: true,
                                                onConfirm: () async {
                                                  Loading.show();
                                                  try {
                                                    var resp = await BookkeepingRepo.deleteBill('${_dataList[index].id}');
                                                    if (resp.code == 1) {
                                                      showToast('删除成功');
                                                      _doSearch();
                                                    } else {
                                                      showToast(resp.msg ?? '删除失败');
                                                    }
                                                  } catch (e) {
                                                    showToast('删除失败 $e');
                                                  } finally {
                                                    Loading.dismiss();
                                                  }
                                                },
                                                onCancel: () {},
                                              );
                                            },
                                          ),
                                          TransactionItemAction(
                                            bgColor: MColor.xFFFFBE4A,
                                            fgColor: MColor.xFFFFFFFF,
                                            icon: Icons.edit,
                                            label: '编辑',
                                            onPressed: () async {
                                              RouterHelper.router
                                                  .pushNamed(Routes.bookkeepingPath, queryParameters: {'logId': '${_dataList[index].id}'}).then((_) {
                                                _doSearch();
                                              });
                                            },
                                          )
                                        ]));
                                  },
                                  separatorBuilder: (context, index) {
                                    return Divider(
                                      height: 0.5,
                                      thickness: 0.5,
                                      color: MColor.xFFD9D9D9,
                                      indent: 15,
                                    );
                                  },
                                  itemCount: _dataList.length),
                            ),
                          ],
                        );
                      }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _headerView {
    return Padding(
      padding: EdgeInsets.fromLTRB(14, 8, 14, 14),
      child: Row(
        children: [
          Text(
            '账目明细',
            style: TextStyle(fontSize: 17, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(
            width: 12,
          ),
          const Spacer(),
          Text('共${_dataList.length}笔', style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFFCB322E)),
        ],
      ),
    );
  }

  Widget get _noDataView {
    return GetBuilder<UserStore>(
        id: 'search_history',
        builder: (context) {
          if (_keywordController.text.isEmpty && UserStore.to.searchHistories.isNotEmpty) {
            return _historyView;
          } else if (_keywordController.text.isNotEmpty) {
            return EmptyView(tips: '搜索无结果');
          }
          return EmptyView();
        });
  }

  Widget get _historyView {
    return Container(
      child: Column(
        children: [
          Row(
            children: [
              const SizedBox(
                width: 14,
              ),
              Text(
                '最近搜索',
                style: TextStyle(fontSize: 17, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
              ),
              const Spacer(),
              IconButton(
                  onPressed: () {
                    showCustomDialog(
                      '确认清空最近搜索',
                      onConfirm: () async {
                        UserStore.to.clearSearchHistory();
                      },
                      okTitle: '确认',
                      cancel: true,
                      onCancel: () {},
                    );
                  },
                  icon: Icon(
                    Icons.delete,
                    size: 14,
                    color: MColor.xFF8C8C8C,
                  )),
              const SizedBox(
                width: 14,
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              const SizedBox(
                width: 14,
              ),
              Expanded(
                child: Wrap(
                  direction: Axis.horizontal,
                  alignment: WrapAlignment.start,
                  spacing: 14.0,
                  runSpacing: 8.0,
                  children: List.generate(UserStore.to.searchHistories.length, (index) {
                    return GestureDetector(
                      onTap: () {
                        _keywordController.text = UserStore.to.searchHistories[index].query;
                        _doSearch();
                      },
                      child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(15)),
                          child: Text(
                            UserStore.to.searchHistories[index].query,
                            style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                          )),
                    );
                  }),
                ),
              ),
              const SizedBox(
                width: 14,
              ),
            ],
          )
        ],
      ),
    );
  }

  Future<void> _doSearch() async {
    if (_keywordController.text.isEmpty) {
      showToast('请输入关键词');
      return;
    }
    UserStore.to.updateSearchHistory(_keywordController.text);
    Loading.show();
    try {
      var resp = await DetailRepo.search(action: '1', keyword: _keywordController.text);
      if (resp.code == 1) {
        _dataList.clear();
        _dataList.addAll(resp.data ?? []);
        setState(() {});
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
  }
}
