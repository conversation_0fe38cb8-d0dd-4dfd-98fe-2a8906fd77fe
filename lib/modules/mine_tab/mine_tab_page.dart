import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/user_model.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/widgets/round_image.dart';

// 用户数据模型
class UserProfile {
  final String name;
  final String avatarUrl;
  final String status;
  final int recordDays;
  final double totalSavings;
  final double nonEssentialExpenses;
  final int totalRecords;
  final bool isVip;
  final int vipDaysLeft;

  UserProfile({
    required this.name,
    required this.avatarUrl,
    required this.status,
    required this.recordDays,
    required this.totalSavings,
    required this.nonEssentialExpenses,
    required this.totalRecords,
    required this.isVip,
    required this.vipDaysLeft,
  });
}

// 用户数据提供者
final userProfileProvider = StateProvider<UserProfile>((ref) {
  return UserProfile(
    name: '薛定谔的猫',
    avatarUrl: 'assets/avatar.png', // 请确保添加此图片资源
    status: '拮据了点，生活就不会慌张......',
    recordDays: 15,
    totalSavings: 200,
    nonEssentialExpenses: 32,
    totalRecords: 32,
    isVip: true,
    vipDaysLeft: 25,
  );
});

class MyProfilePage extends StatefulWidget {
  const MyProfilePage({super.key});

  @override
  State<StatefulWidget> createState() => _MineTabState();
}

class _MineTabState extends State<MyProfilePage> {
  UserInfoResp? _userInfoResp;

  @override
  void initState() {
    _initData();
    super.initState();
  }

  Future<void> _initData() async {
    _userInfoResp = await UserStore.to.getUserInfo();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        // maintainBottomViewPadding: true,
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Image.asset(
                  'assets/images/mine_bg.png',
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.fitWidth,
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      actions: [
                        IconButton(
                            // padding: EdgeInsets.zero,
                            // constraints: const BoxConstraints(),
                            // style: const ButtonStyle(
                            //   tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                            // ),
                            onPressed: () {
                              RouterHelper.router.pushNamed(Routes.appSettingPath);
                            },
                            icon: Image.asset(
                              'assets/images/ic_setting_2.png',
                              width: 24,
                              height: 24,
                              fit: BoxFit.fill,
                            )),
                      ],
                      title: Image.asset(
                        'assets/images/icon_title.png',
                        width: 129,
                        height: 30,
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: RefreshIndicator(
                  onRefresh: () async {
                    await _initData();
                  },
                  child: GetBuilder<UserStore>(builder: (userStore) {
                    return SingleChildScrollView(
                      child: GetBuilder<UserStore>(builder: (userStore) {
                        return Column(
                          children: [
                            _buildUserInfoSection,
                            _buildVipSection,
                            _buildQuickAccessGrid(),
                            _buildPromotionBanner(),
                            _buildSettingsSection(),
                          ],
                        );
                      }),
                    );
                  }),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _buildUserInfoSection {
    List<Map<String, dynamic>> tiles = [
      {'value': _userInfoResp?.accountingDays ?? 0, 'label': '记账天数'},
      {'value': _userInfoResp?.saveMoney ?? 0, 'label': '节省总金额', 'path': Routes.savingPath, 'page_type': 0},
      {'value': _userInfoResp?.necessaryMoney ?? 0, 'label': '非必要金额', 'path': Routes.savingPath, 'page_type': 1},
      {'value': _userInfoResp?.accountingCount ?? 0, 'label': '总笔数'}
    ];
    return Container(
      // color: Colors.yellow,
      margin: EdgeInsets.fromLTRB(14, 11, 14, 0),
      child: Column(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              RouterHelper.router.pushNamed(Routes.appSettingPath).then((_) {
                _initData();
              });
            },
            child: Row(
              children: [
                RoundImage(imageUrl: getImageUrl(_userInfoResp?.userInfo?.avatar ?? ''), radius: 30, size: 60),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                          textAlign: TextAlign.start,
                          text: WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(_userInfoResp?.userInfo?.userName ?? '',
                                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, height: 1.4, color: MColor.xFF1B1C1A)),
                                  const SizedBox(
                                    width: 9,
                                  ),
                                  Container(
                                    decoration: BoxDecoration(color: MColor.xFFC2E2CF, borderRadius: BorderRadius.circular(4)),
                                    padding: EdgeInsets.symmetric(horizontal: 5.5, vertical: 0.5),
                                    child: Text(
                                      'VIP',
                                      style:
                                          TextStyle(color: _userInfoResp?.userInfo?.vipType == '0' ? MColor.xFF999999 : MColor.skin, height: 1.4, fontSize: 12),
                                    ),
                                  ),
                                ],
                              ))),
                      const SizedBox(
                        height: 8,
                      ),
                      Text(
                        _userInfoResp?.userInfo?.bio ?? '',
                        style: TextStyle(color: MColor.xFF999999, height: 1.4, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: tiles.length,
                crossAxisSpacing: 0,
                mainAxisSpacing: 0,
              ),
              itemCount: tiles.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    if (tiles[index]['path']?.isNotEmpty == true) {
                      var extra = {};
                      if (tiles[index].containsKey('page_type')) {
                        extra['page_type'] = tiles[index]['page_type'];
                      }
                      RouterHelper.router.pushNamed(tiles[index]['path'], extra: extra).then((value) {
                        _initData();
                      });
                    }
                  },
                  child: Container(
                    // color: Colors.black,
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          tiles[index]['value'].toString(),
                          style: TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF1B1C1A),
                        ),
                        SizedBox(height: 4),
                        Text(
                          tiles[index]['label'],
                          style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF999999),
                        ),
                      ],
                    ),
                  ),
                );
              })
        ],
      ),
    );
  }

  Widget get _buildVipSection {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.membershipPath);
      },
      child: Container(
        height: 92,
        margin: EdgeInsets.fromLTRB(14, 4, 14, 14),
        padding: EdgeInsets.all(14),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/mine_vip_bg.png",
            ),
            fit: BoxFit.cover,
          ),
          color: MColor.xFFFED58E,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Image.asset(
                  'assets/images/mine_vip_text.png',
                  width: 57,
                  height: 22,
                  fit: BoxFit.fill,
                ),
                Text(
                  _userInfoResp?.vipMemo ?? '',
                  style: TextStyle(height: 1.4, fontSize: 13, color: MColor.xFF553600),
                ),
                Text(_userInfoResp?.vipAdvertisementMemo ?? '', style: TextStyle(height: 1.4, fontSize: 12, color: MColor.skin)),
              ],
            ),
            if (_userInfoResp?.userInfo?.vipType == '0')
              ElevatedButton(
                onPressed: () {
                  RouterHelper.router.pushNamed(Routes.membershipPath);
                },
                child: Text(
                  '立即开通',
                  style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFFFED58E),
                ),
                style: ElevatedButton.styleFrom(
                  elevation: 2,
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  backgroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget get _headSection {
    return Row(
      children: [CachedNetworkImage(imageUrl: ''), Text('aaaa')],
    );
  }

  Widget _buildQuickAccessGrid() {
    final List<Map<String, dynamic>> items = [
      {'icon': 'assets/images/ic_ledger_3.png', 'label': '账本', 'path': Routes.ledgarListPath},
      {'icon': 'assets/images/ic_assets.png', 'label': '资产', 'path': Routes.myAssetsPath},
      {'icon': 'assets/images/ic_repayment.png', 'label': '还款便签', 'path': Routes.repayDiaryPath},
      {'icon': 'assets/images/ic_consume_guide.png', 'label': '消费宝典', 'path': Routes.consumptionGuidePath},
      // {'icon': 'assets/images/ic_best_deal.png', 'label': '羊毛助手'},
      {'icon': 'assets/images/ic_annual_fee.png', 'label': '年费保镖', 'path': Routes.annualFeePlanPath},
      // {'icon': 'assets/images/ic_debt.png', 'label': '讨账指南'},
      // {'icon': 'assets/images/ic_cloud.png', 'label': '云同步'},
    ];

    return Container(
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: MColor.xFFFFFFFF),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          crossAxisSpacing: 0,
          mainAxisSpacing: 14,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              RouterHelper.router.pushNamed(items[index]['path']);
            },
            child: Container(
              // color: Colors.yellow,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(items[index]['icon'], width: 32, height: 32, fit: BoxFit.fill),
                  SizedBox(height: 4),
                  Text(
                    items[index]['label'],
                    style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPromotionBanner() {
    return Container(
      margin: EdgeInsets.all(14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Image.asset('assets/images/mine_ad.png', width: 347, fit: BoxFit.fill),
    );
  }

  Widget _buildSettingsSection() {
    final List<Map<String, dynamic>> settings = [
      {'icon': 'assets/images/ic_feedback.png', 'label': '意见反馈', 'path': Routes.feedbackPath},
      {'icon': 'assets/images/ic_question.png', 'label': '常见问题', 'path': Routes.qaPath},
      {'icon': 'assets/images/ic_about.png', 'label': '关于我们', 'path': Routes.aboutPath},
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(14, 0, 14, 14),
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: settings.map((setting) {
          return ListTile(
            onTap: () {
              RouterHelper.router.pushNamed(setting['path']);
            },
            leading: Image.asset(
              setting['icon'],
              width: 20,
              height: 20,
              fit: BoxFit.fill,
            ),
            title: Text(setting['label'], style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF777777)),
            trailing: Icon(
              Icons.chevron_right,
              color: MColor.xFF999999,
              size: 16,
            ),
          );
        }).toList(),
      ),
    );
  }
}
