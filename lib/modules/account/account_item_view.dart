import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/price_view.dart';

class AccountItemView extends StatelessWidget {
  final AccountModel accountModel;
  final Function()? onDataChanged;

  const AccountItemView(this.accountModel, {this.onDataChanged, super.key});
  @override
  Widget build(BuildContext context) {
    String? icon;
    String? subIcon;
    String? title;
    String? subTitle;
    int? isJoinTotal;
    isJoinTotal = accountModel.isJoinTotal;
    icon = accountModel.accountIcon;
    if (accountModel.cardType == '1') {
      title = '储蓄卡';
      subTitle = '${accountModel.bankName} (${accountModel.accountName})';
      subIcon = accountModel.bankIcon;
    } else if (accountModel.cardType == '2') {
      title = '信用卡';
      subTitle = '${accountModel.bankName} (${accountModel.accountName})';
      subIcon = accountModel.bankIcon;
    } else if (accountModel.cardType == '3') {
      if (accountModel.subType == '1') {
        title = '微信';
        subTitle = accountModel.accountName;
      } else if (accountModel.subType == '2') {
        title = '支付宝';
        subTitle = accountModel.accountName;
      } else if (accountModel.subType == '3') {
        title = '网络账户';
        subTitle = accountModel.accountName;
      }
    } else if (accountModel.cardType == '4') {
      //现金账户
      title = '现金账户';
      subTitle = accountModel.accountName;
    } else if (accountModel.cardType == '5') {
      title = '投资账户';
      subTitle = accountModel.accountName;
    } else if (accountModel.cardType == '6') {
      title = '借贷往来';
      subTitle = accountModel.accountName;
    } else if (accountModel.cardType == '7') {
      title = '捐赠账户';
      subTitle = accountModel.accountName;
    } else if (accountModel.cardType == '8') {
      title = '大笔资产';
      subTitle = accountModel.accountName;
    }
    //储蓄账户
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.accountDetailPath, extra: {'accountId': '${accountModel.id}'}).then((value) {
          // _getData();
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 14, horizontal: 14),
        margin: EdgeInsets.symmetric(horizontal: 14),
        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(20)),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: CachedNetworkImage(
                imageUrl: getImageUrl(icon ?? ''),
                width: 40,
                height: 40,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: BoxDecoration(
                      color: MColor.xFFECECEC,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Image.asset(
                    'assets/images/ic_launcher.png',
                    width: 40,
                    height: 40,
                    fit: BoxFit.fill,
                  );
                },
              ),
            ),
            const SizedBox(
              width: 14,
            ),
            Expanded(
                child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title ?? '',
                      style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        _onAmountTapped(context,
                            oldValue: accountModel.cardType != '2' ? accountModel.balance : null,
                            billed: accountModel.billAmount,
                            notBilled: accountModel.notBilledAmount);
                      },
                      child: _buildAmountWidget(),
                    )
                  ],
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (subIcon?.isNotEmpty == true) ...{
                      CachedNetworkImage(
                        imageUrl: getImageUrl(subIcon ?? ''),
                        width: 20,
                        height: 20,
                        fit: BoxFit.cover,
                        placeholder: (ctx, e) {
                          return Container(
                            decoration: BoxDecoration(
                              color: MColor.xFFECECEC,
                            ),
                          );
                        },
                        errorWidget: (ctx, e, x) {
                          return Image.asset(
                            'assets/images/ic_launcher.png',
                            width: 20,
                            height: 20,
                            fit: BoxFit.fill,
                          );
                        },
                      ),
                      const SizedBox(width: 10)
                    },
                    Text(subTitle ?? '', style: TextStyle(height: 1.4, fontSize: 13, color: MColor.xFF999999)),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        _onJoinTotalTapped(context);
                      },
                      child: Text(
                        isJoinTotal == 1 ? '计入总资产' : (isJoinTotal == 2 ? '不计入总资产' : ''),
                        style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
                      ),
                    )
                  ],
                )
              ],
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountWidget() {
    if (accountModel.cardType == '2') {
      // 信用卡显示已出账单和未出账单
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            children: [
              Text(
                '已出',
                style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
              ),
              PriceView(
                price: PriceInfo.parsePrice(accountModel.billAmount ?? '0.00'),
                integerFontSize: 15,
                fractionalFontSize: 12,
                textColor: MColor.xFF1B1C1A,
                fontWeight: FontWeight.w500,
                prefix: '-',
                prefixStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                showSymbol: false,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                '未出',
                style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
              ),
              PriceView(
                price: PriceInfo.parsePrice(accountModel.notBilledAmount ?? '0.00'),
                integerFontSize: 15,
                fractionalFontSize: 12,
                textColor: MColor.xFF1B1C1A,
                fontWeight: FontWeight.w500,
                prefix: '-',
                prefixStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                showSymbol: false,
              ),
            ],
          ),
        ],
      );
    } else {
      // 其他账户类型显示余额
      String balanceAmount = accountModel.balance ?? '0.00';
      String prefix = '';

      if (accountModel.cardType == '6' && accountModel.subType == '1') {
        // 借贷往来中的借出，显示负号
        prefix = '-';
      }

      return PriceView(
        price: PriceInfo.parsePrice(balanceAmount),
        integerFontSize: 15,
        fractionalFontSize: 12,
        textColor: MColor.xFF1B1C1A,
        fontWeight: FontWeight.w500,
        prefix: prefix.isNotEmpty ? prefix : null,
        prefixStyle: prefix.isNotEmpty ? TextStyle(fontSize: 15, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A) : null,
      );
    }
  }

  void _onAmountTapped(BuildContext context, {String? oldValue, String? notBilled, String? billed}) {
    Widget widget;
    TextEditingController controller1 = TextEditingController(text: oldValue);
    TextEditingController controller2 = TextEditingController(text: notBilled);
    TextEditingController controller3 = TextEditingController(text: billed);
    widget = Container(
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
      child: Column(
        children: [
          if (oldValue?.isNotEmpty == true)
            AccountInputTile(
                leadingText: '修改金额',
                textController: controller1,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          if (billed?.isNotEmpty == true)
            AccountInputTile(
                leadingText: '修改已出账单',
                textController: controller3,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
          if (notBilled?.isNotEmpty == true)
            AccountInputTile(
                leadingText: '修改未出账单',
                textController: controller2,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')), TwoDecimalInputFormatter()]),
        ],
      ),
    );
    // }
    RouterHelper.router.pushNamed(Routes.customPopupPath, extra: {
      'title': '修改金额',
      'widget': widget,
      'onConfirm': () async {
        if (oldValue != (controller1.text)) {
          Loading.show();
          try {
            var resp = await AccountRepo.updateAccountAmount('${accountModel.id}',
                amount: (oldValue?.isNotEmpty == true ? controller1.text : ''),
                billAmount: (billed?.isNotEmpty == true ? controller3.text : ''),
                notBillAmount: notBilled?.isNotEmpty == true ? controller2.text : '');
            if (resp.code == 1) {
              showToast('修改成功');
              onDataChanged?.call();
            } else {
              showToast(resp.msg ?? '修改失败');
            }
          } catch (e) {
            showToast('修改失败 $e');
          } finally {
            Loading.dismiss();
          }
        }
      }
    }).then((_) {});
  }

  void _onJoinTotalTapped(BuildContext context) {
    bool val = accountModel.isJoinTotal == 1;
    Widget widget = Container(
      height: 50,
      padding: EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
      child: AccountSwitchTile(
        leadingText: '是否计入资产',
        initValue: val,
        onChanged: (v) async {
          val = v;
          Loading.show();
          try {
            var resp = await AccountRepo.updateJoinTotal('${accountModel.id}', val ? '1' : '2');
            if (resp.code == 1) {
              onDataChanged?.call();
              RouterHelper.router.pop();
              showToast('修改成功');
            } else {
              showToast(resp.msg ?? '修改成功');
            }
          } catch (e) {
            showToast('修改成功 $e');
          } finally {
            Loading.dismiss();
          }
        },
      ),
    );

    RouterHelper.router.pushNamed(Routes.customPopupPath, extra: {
      'title': '是否计入资产',
      'widget': widget,
      'onConfirm': () async {
        // if (val != (accountModel.isJoinTotal == 1)) {
        //   Loading.show();
        //   try {
        //     var resp = await AccountRepo.updateJoinTotal('${accountModel.id}', val ? '1' : '2');
        //     if (resp.code == 1) {
        //       onDataChanged?.call();
        //       RouterHelper.router.pop();
        //       showToast('修改成功');
        //     } else {
        //       showToast(resp.msg ?? '修改成功');
        //     }
        //   } catch (e) {
        //     showToast('修改成功 $e');
        //   } finally {
        //     Loading.dismiss();
        //   }
        // }
      }
    }).then((_) {});
  }
}
