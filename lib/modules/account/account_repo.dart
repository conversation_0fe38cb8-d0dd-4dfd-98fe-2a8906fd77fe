import 'dart:convert';

import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/account/account_model.dart';

class AccountRepo {
  static Future<BaseModel<dynamic>> addSavingCard(
      {required String accountType,
      int? id,
      String? balance,
      String? price,
      String? isJoinTotal,
      String? bankId,
      String? bankName,
      String? cardNo,
      String? name,
      String? accountName,
      String? networkType,
      String? accountIncome,
      String? date,
      String? loanType,
      String? donationType,
      String? propertyPrice,
      String? propertyType,
      String? quota,
      String? repaymentDate,
      String? issuedBill,
      String? noissuedBill,
      String? annualFee,
      String? annualPrice,
      String? billDate,
      String? availableAmount,
      String? consumptionAmount,
      String? propertyProportion,
      String? otherAccountId,
      List<PromotionCondition>? promotionConditions,
      AnnualFeeCondition? annualFeeCondition,
      String? memo}) async {
    var data = <String, dynamic>{
      'accountType': accountType,
    };
    if (id != null) {
      data['accountId'] = id;
    }
    if (promotionConditions?.isNotEmpty == true) {
      data['promotionConditions'] = promotionConditions!;
    }
    if (annualFeeCondition != null) {
      data['exemptionConditions'] = annualFeeCondition!;
    }
    if (isJoinTotal?.isNotEmpty == true) {
      data['isJoinTotal'] = isJoinTotal!;
    }
    if (quota?.isNotEmpty == true) {
      data['quota'] = quota!;
    }
    if (annualFee?.isNotEmpty == true) {
      data['annualFee'] = annualFee!;
    }
    if (billDate?.isNotEmpty == true) {
      data['billDate'] = billDate!;
    }
    if (annualPrice?.isNotEmpty == true) {
      data['annualPrice'] = annualPrice!;
    }
    if (repaymentDate?.isNotEmpty == true) {
      data['repaymentDate'] = repaymentDate!;
    }
    if (issuedBill?.isNotEmpty == true) {
      data['issuedBill'] = issuedBill!;
    }
    if (noissuedBill?.isNotEmpty == true) {
      data['noissuedBill'] = noissuedBill!;
    }
    if (availableAmount?.isNotEmpty == true) {
      data['availableAmount'] = availableAmount!;
    }
    if (consumptionAmount?.isNotEmpty == true) {
      data['consumptionAmount'] = consumptionAmount!;
    }
    if (propertyPrice?.isNotEmpty == true) {
      data['propertyPrice'] = propertyPrice!;
    }
    if (propertyProportion?.isNotEmpty == true) {
      data['propertyProportion'] = propertyProportion!;
    }
    if (propertyType?.isNotEmpty == true) {
      data['propertyType'] = propertyType!;
    }
    if (bankId?.isNotEmpty == true) {
      data['bankId'] = bankId!;
    }
    if (name?.isNotEmpty == true) {
      data['name'] = name!;
    }
    if (price?.isNotEmpty == true) {
      data['price'] = price!;
    }
    if (date?.isNotEmpty == true) {
      data['date'] = date!;
    }
    if (loanType?.isNotEmpty == true) {
      data['loanType'] = loanType!;
    }
    if (donationType?.isNotEmpty == true) {
      data['donateType'] = donationType!;
    }
    if (balance?.isNotEmpty == true) {
      data['balance'] = balance!;
    }
    if (bankName?.isNotEmpty == true) {
      data['bankName'] = bankName!;
    }
    if (cardNo?.isNotEmpty == true) {
      data['cardNo'] = cardNo!;
    }
    if (accountName?.isNotEmpty == true) {
      data['accountName'] = accountName!;
    }
    if (networkType?.isNotEmpty == true) {
      data['networkType'] = networkType!;
    }
    if (accountIncome?.isNotEmpty == true) {
      data['accountIncome'] = accountIncome!;
    }
    if (memo?.isNotEmpty == true) {
      data['memo'] = memo!;
    }
    if (otherAccountId?.isNotEmpty == true) {
      data['otherAccountId'] = otherAccountId!;
    }

    var resp = await HttpUtil().post('api/card/addAccount', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<BankItem>>> getBankList() async {
    var resp = await HttpUtil().post('api/card/bankList');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => BankItem.fromJson(e)).toList());
  }

  static Future<BaseModel<dynamic>> deleteAccount(int? id) async {
    var resp = await HttpUtil().post('api/card/delCard', data: {'accountId': id});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> addBorrowLend(String borrowType, String fromId, String toId, String amount, String memo) async {
    var resp = await HttpUtil()
        .post('api/bookkeeping/addBorrow', data: {'borrowType': borrowType, 'otherSide': toId, 'accountId': fromId, 'money': amount, 'memo': memo});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<AccountModel>> getAccountDetail(int accountId) async {
    var resp = await HttpUtil().post('api/card/accountDetail', data: {'accountId': accountId});
    return BaseModel.fromJson(resp, (json) => AccountModel.fromJson(json));
  }

  static Future<BaseModel<dynamic>> updateJoinTotal(String accountId, String action) async {
    var resp = await HttpUtil().post('api/card/updAccountIsJoinTotal', data: {'accountId': accountId, 'action': action});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> updateAccountAmount(String accountId, {String? amount, String? billAmount, String? notBillAmount}) async {
    var data = {};
    data['accountId'] = accountId;
    if (amount?.isNotEmpty == true) {
      data['amount'] = amount!;
    }
    if (billAmount?.isNotEmpty == true) {
      data['billAmount'] = billAmount!;
    }
    if (notBillAmount?.isNotEmpty == true) {
      data['notBillAmount'] = notBillAmount!;
    }
    var resp = await HttpUtil().post('api/card/updAccountPrice', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<AccountDetailAndLogResp>> getAccountDetailAndLog(
    String accountId,
  ) async {
    var resp = await HttpUtil().post('api/card/getAccountDetailAndLog', data: {'accountId': accountId});
    return BaseModel.fromJson(resp, (json) => AccountDetailAndLogResp.fromJson(json));
  }
}
