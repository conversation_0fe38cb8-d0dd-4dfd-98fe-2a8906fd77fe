import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_common_widgets.dart';
import 'package:qiazhun/modules/account/account_item_view.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/loading_view.dart';

class AccountListPage extends StatefulWidget {
  final bool isSelectMode;
  final String? limitAccount;
  final int? initialTab;
  final int? initialSubTab;

  const AccountListPage({required this.isSelectMode, this.limitAccount, this.initialTab, this.initialSubTab, super.key});

  @override
  State<StatefulWidget> createState() => _AccountListState();
}

class _AccountListState extends State<AccountListPage> {
  bool _isLoading = true;

  final List<AccountModel> _accountList = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    await BookkeepingStore.to.getAccountList();
    _accountList.clear();
    if (widget.limitAccount?.isNotEmpty == true) {
      List<AccountModel> tmpList = [];
      for (var item in BookkeepingStore.to.accountList) {
        if (widget.limitAccount == '1') {
          if (item.cardType == '6' && item.subType == '1') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '2') {
          if (item.cardType == '6' && item.subType == '2') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '3') {
          if (item.cardType != '6') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '4') {
          if (item.cardType == '1' || item.cardType == '3' || item.cardType == '4') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '5') {
          if (item.cardType != '5' && item.cardType != '6' && item.cardType != '7' && item.cardType != '8') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '6') {
          if (item.cardType == '1' || item.cardType == '3' || item.cardType == '4') {
            tmpList.add(item);
          }
        } else if (widget.limitAccount == '7') {
          if (item.cardType == '1' || item.cardType == '2' || item.cardType == '3' || item.cardType == '4') {
            tmpList.add(item);
          }
        }
      }
      _accountList.addAll(tmpList);
    } else {
      _accountList.addAll(BookkeepingStore.to.accountList);
    }

    _isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      actions: [
                        IconButton(
                            onPressed: () {
                              RouterHelper.router
                                  .pushNamed(Routes.addAccountPath, extra: {'initialTab': widget.initialTab, 'initialSubTab': widget.initialSubTab}).then((_) {
                                _loadData();
                              });
                            },
                            icon: Image.asset(
                              'assets/images/ic_add_3.png',
                              width: 22,
                              height: 22,
                              fit: BoxFit.fill,
                            )),
                      ],
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '账户列表',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: RefreshIndicator(
                    onRefresh: () async {
                      await _loadData();
                    },
                    child: Builder(builder: (context) {
                      if (_isLoading) {
                        return LoadingView();
                      }
                      if (_accountList.isEmpty) {
                        return EmptyView();
                      }
                      return ListView.separated(
                          padding: EdgeInsets.zero,
                          itemBuilder: (context, index) {
                            return _itemView(_accountList[index]);
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(
                              height: 13,
                            );
                          },
                          itemCount: _accountList.length);
                    }),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _deleteAccount(AccountModel data) async {
    Loading.show();
    try {
      var resp = await AccountRepo.deleteAccount(data.id);
      if (resp.code == 1) {
        await _loadData();
        showToast('删除成功');
      } else {
        showToast(resp.msg ?? '删除失败');
      }
    } catch (e) {
      showToast('删除失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Widget _itemView(AccountModel data) {
    return Slidable(
      key: ValueKey(data.id),
      // The end action pane is the one at the right or the bottom side.
      endActionPane: widget.isSelectMode
          ? null
          : ActionPane(
              motion: ScrollMotion(),
              children: [
                SlidableAction(
                  onPressed: (context) {
                    showCustomDialog(
                      '确认删除',
                      content: '删除后不可恢复，请确认是否删除',
                      cancel: true,
                      onConfirm: () async {
                        await _deleteAccount(data);
                      },
                      onCancel: () {},
                    );
                  },
                  backgroundColor: MColor.xFFFF7858,
                  foregroundColor: MColor.xFFFFFFFF,
                  icon: Icons.delete,
                  label: '删除',
                ),
                SlidableAction(
                  onPressed: (context) async {
                    RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data.id}).then((value) {
                      _loadData();
                    });
                  },
                  backgroundColor: MColor.xFFFFBE4A,
                  foregroundColor: MColor.xFFFFFFFF,
                  icon: Icons.edit,
                  label: '编辑',
                )
              ],
            ),
      child: GestureDetector(
        onTap: () {
          if (widget.isSelectMode) {
            RouterHelper.router.pop({'selected': data});
          }
        },
        child: AccountItemView(
          data,
          onDataChanged: () {
            _loadData();
          },
        ),
      ),
    );
  }
}
