import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:lunar/calendar/Lunar.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/calendar/calendar_model.dart';
import 'package:qiazhun/modules/calendar/calendar_repo.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarPage extends StatefulWidget {
  const CalendarPage({super.key});

  @override
  State<StatefulWidget> createState() => _CalendarState();
}

class _CalendarState extends State<CalendarPage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  late DateTime _selectedDay;

  late DateTime _currentMonth;

  PageController? _pageController;

  CalendarResp? _calendarData;

  @override
  void initState() {
    logger.i('Calendar init');
    _selectedDay = DateTime.now();
    _currentMonth = DateTime(_selectedDay.year, _selectedDay.month, 1);
    _getData();
    super.initState();
  }

  Future<void> _getData() async {
    Loading.show();

    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    DateFormat month = DateFormat("yyyy-MM");
    try {
      var resp = await CalendarRepo.getDayFlowingWaterLog(date: dateFormat.format(_selectedDay), month: month.format(_currentMonth));
      if (resp.code == 1) {
        setState(() {
          _calendarData = resp.data;
        });
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      logger.e('CalendarPage getData error $e');
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
  }

  @override
  void didUpdateWidget(CalendarPage oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      // floatingActionButton: IconButton(
      //   padding: EdgeInsets.zero,
      //   constraints: const BoxConstraints(),
      //   style: const ButtonStyle(
      //     tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
      //   ),
      //   icon: Image.asset(
      //     'assets/images/ic_add.png',
      //     height: 50,
      //     width: 50,
      //   ),
      //   onPressed: () {},
      // ),
      body: SafeArea(
        // maintainBottomViewPadding: true,
        top: false,
        bottom: false,
        child: Stack(
          children: [
            Container(
              color: const Color(0xFFF5F5F5),
            ),
            Positioned(
              // top: 0,
              child: Container(
                height: 203,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                ),
              ),
            ),
            Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: AppBar(
                    scrolledUnderElevation: 0,
                    backgroundColor: Colors.transparent,
                    centerTitle: true,
                    leading: IconButton(
                      icon: Image.asset(
                        'assets/images/ic_back.png',
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () {
                        RouterHelper.router.pop();
                      },
                    ),
                    title: Image.asset(
                      'assets/images/icon_title.png',
                      width: 129,
                      height: 30,
                    ))),
            Positioned.fill(
              top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
              // top: 0,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _calendarView,
                  Expanded(
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (index == 0) {
                          return _transactionHeaderView;
                        } else {
                          return TransactionItemView(_calendarData!.flowingWaterLog[index - 1], actions: [
                            TransactionItemAction(
                              label: '删除',
                              icon: Icons.delete,
                              bgColor: MColor.xFFFF7858,
                              fgColor: MColor.xFFFFFFFF,
                              onPressed: () {
                                showCustomDialog(
                                  '确认删除',
                                  content: '删除后不可恢复，请确认是否删除',
                                  cancel: true,
                                  onConfirm: () async {
                                    Loading.show();
                                    try {
                                      var resp = await BookkeepingRepo.deleteBill('${_calendarData!.flowingWaterLog[index - 1].id}');
                                      if (resp.code == 1) {
                                        showToast('删除成功');
                                        _getData();
                                      } else {
                                        showToast(resp.msg ?? '删除失败');
                                      }
                                    } catch (e) {
                                      showToast('删除失败 $e');
                                    } finally {
                                      Loading.dismiss();
                                    }
                                  },
                                  onCancel: () {},
                                );
                              },
                            ),
                            TransactionItemAction(
                              bgColor: MColor.xFFFFBE4A,
                              fgColor: MColor.xFFFFFFFF,
                              icon: Icons.edit,
                              label: '编辑',
                              onPressed: () async {
                                RouterHelper.router
                                    .pushNamed(Routes.bookkeepingPath, queryParameters: {'logId': '${_calendarData!.flowingWaterLog[index - 1].id}'}).then((_) {
                                  _getData();
                                });
                              },
                            )
                          ]);
                        }
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(
                          height: 17,
                        );
                      },
                      itemCount: (_calendarData?.flowingWaterLog.length ?? 0) + 1,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget get _transactionHeaderView {
    return Row(
      children: [
        const SizedBox(
          width: 14,
        ),
        Text(
          '流水',
          style: TextStyle(height: 1.4, fontSize: 17, color: MColor.xFF1B1C1A),
        ),
        const Spacer(),
        Text(
          '收入：¥${_calendarData?.total['totalIncome']}',
          style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
        ),
        const SizedBox(
          width: 14,
        ),
        Text(
          '支出：¥${_calendarData?.total['totalPayment']}',
          style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF999999),
        ),
        const SizedBox(
          width: 14,
        ),
      ],
    );
  }

  Widget get _calendarView {
    return Column(
      children: [
        // const SizedBox(
        //   height: 12,
        // ),
        Row(
          children: [
            const SizedBox(
              width: 14,
            ),
            Text(
              '${_currentMonth.year}年${_currentMonth.month}月',
              style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF2E2C24),
            ),
            const Spacer(),
            IconButton(
                onPressed: () {
                  _pageController?.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                },
                icon: Icon(
                  Icons.arrow_back_ios,
                  size: 16,
                )),
            const SizedBox(
              width: 28,
            ),
            IconButton(
                onPressed: () {
                  _pageController?.nextPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                },
                icon: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                )),
            const SizedBox(
              width: 14,
            ),
          ],
        ),
        // const SizedBox(
        //   height: 14,
        // ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 13, vertical: 16),
          decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.now(),
            locale: 'zh',
            focusedDay: _focusedDay,
            currentDay: _selectedDay,
            calendarFormat: _calendarFormat,
            headerVisible: false,
            availableCalendarFormats: const {CalendarFormat.month: 'Month'},
            onDaySelected: (selectedDay, focusedDay) {
              logger.i('Calendar onDaySelect $selectedDay $focusedDay');
              if (_selectedDay != selectedDay || _focusedDay != focusedDay) {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
                _currentMonth = DateTime(_focusedDay.year, _focusedDay.month);
                _getData();
              }
            },
            onCalendarCreated: (pageController) {
              _pageController = pageController;
            },
            onPageChanged: (DateTime focusedDay) {
              logger.i('Calendar onPageChanged $focusedDay');
              _focusedDay = focusedDay;
              _currentMonth = DateTime(_focusedDay.year, _focusedDay.month);
              _getData();
            },
            calendarBuilders: CalendarBuilders(
              dowBuilder: (context, day) {
                String weekday = DateFormat.E('zh').format(day);
                return Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${weekday}',
                        style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF777777),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                );
              },
              todayBuilder: (context, day, focusedDay) {
                return Container(
                    decoration: BoxDecoration(color: MColor.skin.withOpacity(0.1), borderRadius: BorderRadius.circular(10)),
                    child: _dailyCellView(context, day));
              },
              outsideBuilder: (context, day, focusedDay) {
                return SizedBox();
              },
              disabledBuilder: (context, day, focusedDay) {
                return _dailyCellView(context, day);
              },
              defaultBuilder: (context, day, focusedDay) {
                return _dailyCellView(context, day);
              },
              selectedBuilder: (context, day, focusedDay) {
                logger.i('Calendar selected $day');
                return Container(
                    decoration: BoxDecoration(color: MColor.skin.withOpacity(0.1), borderRadius: BorderRadius.circular(10)),
                    child: _dailyCellView(context, day));
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _dailyCellView(context, day) {
    Lunar lunar = Lunar.fromDate(day);
    CalendarLog? log = _calendarData?.monthFlowingWaterLog.firstWhereOrNull(
      (element) {
        DateFormat dateFormat = DateFormat("yyyy-MM-dd");
        return element.date == dateFormat.format(day);
      },
    );
    // logger.i('find day data ${log != null ? day : 'nnnnn'}');
    return SizedBox(
      height: 64,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(
            height: 2,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${day.day}',
                style: TextStyle(fontSize: 16.0, height: 1.4, color: MColor.xFF1B1C1A),
              ),
            ],
          ),
          if (log != null) ...{
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '+${log.totalIncome}',
                  style: TextStyle(fontSize: 10.0, height: 1.4, color: MColor.xFFCB322E),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '-${log.totalPayment}',
                  style: TextStyle(fontSize: 10.0, height: 1.4, color: MColor.xFF4C9B93),
                ),
              ],
            ),
          } else ...{
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  lunar.getDayInChinese(),
                  style: TextStyle(fontSize: 10.0, height: 1.4, color: MColor.xFF999999),
                ),
              ],
            ),
          }
        ],
      ),
    );
  }

  Widget _weekdayCellView(int day) {
    return Text(
      '周${day}',
      style: TextStyle(fontSize: 16.0),
    );
  }
}

class _CalendarView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<_CalendarView> {
  List<String> _weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [_weekHeaderView],
    );
  }

  Widget get _weekHeaderView {
    return SizedBox(
      height: 50,
      child: Row(
        children: List.generate(_weekDays.length, (index) {
          return Expanded(
              child: Text(
            _weekDays[index],
            textAlign: TextAlign.center,
          ));
        }),
      ),
    );
  }
}
