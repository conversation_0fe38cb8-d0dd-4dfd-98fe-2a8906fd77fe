import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class BookkeepingDetailPage extends StatefulWidget {
  final String bookkeepingNumber;
  final String? bookkeepingName;

  const BookkeepingDetailPage({
    required this.bookkeepingNumber,
    this.bookkeepingName,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _BookkeepingDetailPageState();
}

class _BookkeepingDetailPageState extends State<BookkeepingDetailPage> {
  bool _isLoading = true;
  BookkeepingDetailResp? _detailData;
  String _selectedTimeType = '1'; // 1=明细 2=月报 3=年报 4=总报
  String? _selectedTimeInterval;

  final Map<String, String> _timeTypeNames = {
    '1': '明细',
    '2': '月报',
    '3': '年报',
    '4': '总报',
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await BookkeepingRepo.getBookkeepingDetailWithLogs(
        bookkeepingNumber: widget.bookkeepingNumber,
        timeType: _selectedTimeType,
        timeInterval: _selectedTimeInterval,
      );

      if (response.code == 1 && response.data != null) {
        _detailData = response.data;
      } else {
        showToast(response.msg ?? '获取数据失败');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset(
            'assets/images/ic_back.png',
            width: 24,
            height: 24,
          ),
          onPressed: () {
            RouterHelper.router.pop();
          },
        ),
        title: Text(
          widget.bookkeepingName ?? '账本详情',
          style: const TextStyle(
            color: MColor.xFF1B1C1A,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: MColor.xFF1B1C1A),
            onPressed: () {
              // 跳转到记账页面
              RouterHelper.router.pushNamed('/bookkeeping').then((_) {
                _loadData();
              });
            },
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        color: MColor.skin,
      ),
    );
  }

  Widget _buildContent() {
    if (_detailData == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(),
            const SizedBox(height: 16),
            _buildTimeTypeSelector(),
            const SizedBox(height: 16),
            _buildTransactionList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 本月结余
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '本月结余',
                style: TextStyle(
                  color: MColor.xFF1B1C1A,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              PriceView(
                price: PriceInfo.parsePrice(_detailData?.monthBalance ?? '0.00'),
                integerFontSize: 24,
                fractionalFontSize: 18,
                textColor: _isPositive(_detailData?.monthBalance) ? MColor.xFFCB322E : MColor.skin,
                fontWeight: FontWeight.w600,
              ),
            ],
          ),
          const SizedBox(height: 20),
          // 收入支出
          Row(
            children: [
              Expanded(
                child: _buildIncomeExpenseItem(
                  '本月收入',
                  _detailData?.monthIncome ?? '0.00',
                  MColor.xFFCB322E,
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildIncomeExpenseItem(
                  '本月支出',
                  _detailData?.monthExpenditure ?? '0.00',
                  MColor.skin,
                  Icons.trending_down,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeExpenseItem(String title, String amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          PriceView(
            price: PriceInfo.parsePrice(amount),
            integerFontSize: 16,
            fractionalFontSize: 12,
            textColor: color,
            fontWeight: FontWeight.w600,
          ),
        ],
      ),
    );
  }

  bool _isPositive(String? amount) {
    if (amount == null) return true;
    final value = double.tryParse(amount) ?? 0.0;
    return value >= 0;
  }

  Widget _buildTimeTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: _timeTypeNames.entries.map((entry) {
          final isSelected = _selectedTimeType == entry.key;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                if (_selectedTimeType != entry.key) {
                  setState(() {
                    _selectedTimeType = entry.key;
                  });
                  _loadData();
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? MColor.skin : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  entry.value,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : MColor.xFF999999,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTransactionList() {
    final logs = _detailData?.flowingWaterLog ?? [];

    if (logs.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '暂无流水记录',
            style: TextStyle(
              color: MColor.xFF999999,
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return Column(
      children: logs.map((log) => _buildDateSection(log)).toList(),
    );
  }

  Widget _buildDateSection(BookkeepingDateLog log) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 日期头部
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  _formatDate(log.date ?? ''),
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  log.weekChinese ?? '',
                  style: const TextStyle(
                    color: MColor.xFF999999,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
                  '收 ${log.income ?? '0.00'}',
                  style: const TextStyle(
                    color: MColor.xFFCB322E,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '支 ${log.expense ?? '0.00'}',
                  style: const TextStyle(
                    color: MColor.skin,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          // 交易项目
          ...log.items?.map((item) => _buildTransactionItem(item)) ?? [],
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      if (dateStr.isEmpty) return '';
      final date = DateTime.parse(dateStr);
      return '${date.month}月${date.day}日';
    } catch (e) {
      return dateStr;
    }
  }

  Widget _buildTransactionItem(BookkeepingLogItem item) {
    final isIncome = item.type == '1';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFF5F5F5), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 分类图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: (isIncome ? MColor.xFFCB322E : MColor.skin).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              isIncome ? Icons.trending_up : Icons.trending_down,
              color: isIncome ? MColor.xFFCB322E : MColor.skin,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // 交易详情
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.categoryName ?? '未知分类',
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (item.memo?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.memo!,
                    style: const TextStyle(
                      color: MColor.xFF999999,
                      fontSize: 12,
                    ),
                  ),
                ],
                if (item.accountName?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.accountName!,
                    style: const TextStyle(
                      color: MColor.xFF999999,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // 金额
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              PriceView(
                price: PriceInfo.parsePrice(item.money ?? '0.00'),
                integerFontSize: 17,
                fractionalFontSize: 14,
                textColor: isIncome ? MColor.xFFCB322E : MColor.skin,
                fontWeight: FontWeight.w500,
                prefix: isIncome ? '+' : '-',
                prefixStyle: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                  color: isIncome ? MColor.xFFCB322E : MColor.skin,
                ),
                showSymbol: false,
              ),
              if (item.time?.isNotEmpty == true) ...[
                const SizedBox(height: 2),
                Text(
                  _formatTime(item.time!),
                  style: const TextStyle(
                    color: MColor.xFF999999,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  String _formatTime(String timeStr) {
    try {
      final time = DateTime.parse(timeStr);
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeStr;
    }
  }
}
