import 'package:json_annotation/json_annotation.dart';

part 'bookkeeping_model.g.dart';

@JsonSerializable()
class BookkeepingInfo extends Object {
  @Json<PERSON><PERSON>(name: 'bookkeepingId')
  int? bookkeepingId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  @Json<PERSON><PERSON>(name: 'accountBookName')
  String? accountBookName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bookkeepingIcon')
  String? bookkeepingIcon;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'memo')
  String? memo;
  @Json<PERSON>ey(name: 'isJoinTotal')
  String? isJoinTotal;

  @Json<PERSON>ey(name: 'billCount')
  int? billCount;

  BookkeepingInfo(this.bookkeepingId, this.bookkeepingNumber, this.accountBookName, this.bookkeepingIcon, this.isJoinTotal, this.memo, this.billCount);

  factory BookkeepingInfo.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingInfoFromJson(srcJson);
}

@JsonSerializable()
class CategoryItem extends Object {
  @Json<PERSON>ey(name: 'bookkeepingCategoryId')
  int? bookkeepingCategoryId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bookkeepingCategoryType')
  String? bookkeepingCategoryType;

  @JsonKey(name: 'bookkeepingCategoryLevel')
  String? bookkeepingCategoryLevel;

  @JsonKey(name: 'bookkeepingCategoryIcon')
  String? bookkeepingCategoryIcon;

  @JsonKey(name: 'bookkeepingCategoryName')
  String? bookkeepingCategoryName;

  @JsonKey(name: 'createTime')
  String? createTime;

  @JsonKey(name: 'threeCategoryList')
  List<CategoryItem>? threeCategoryList;
  @JsonKey(name: 'pid')
  num? pid;

  CategoryItem(this.bookkeepingCategoryId, this.bookkeepingCategoryType, this.bookkeepingCategoryLevel, this.bookkeepingCategoryIcon,
      this.bookkeepingCategoryName, this.createTime, this.pid, this.threeCategoryList);

  factory CategoryItem.fromJson(Map<String, dynamic> srcJson) => _$CategoryItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);
}

@JsonSerializable()
class BookkeepingCategoryResp {
  @JsonKey(name: 'expenses')
  List<CategoryItem>? expenses;
  @JsonKey(name: 'income')
  List<CategoryItem>? income;
  @JsonKey(name: 'officialExpenses')
  List<CategoryItem>? officialExpenses;
  @JsonKey(name: 'officialIncome')
  List<CategoryItem>? officialIncome;

  BookkeepingCategoryResp(this.expenses, this.income, this.officialExpenses, this.officialIncome);

  factory BookkeepingCategoryResp.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingCategoryRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingCategoryRespToJson(this);
}

@JsonSerializable()
class BillDetailInfo extends Object {
  @JsonKey(name: 'bookkeepingId')
  int? bookkeepingId;

  @JsonKey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  @JsonKey(name: 'bookkeepingName')
  String? bookkeepingName;

  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'icon')
  String? icon;

  @JsonKey(name: 'iconImage')
  String? iconImage;

  @JsonKey(name: 'categoryId')
  int? categoryId;

  @JsonKey(name: 'money')
  String? money;

  @JsonKey(name: 'after')
  String? after;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'week')
  String? week;

  @JsonKey(name: 'categoryName')
  String? categoryName;

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'accountId')
  int? accountId;

  @JsonKey(name: 'accountType')
  String? accountType;

  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'isNecessaryStatus')
  String? isNecessaryStatus;

  @JsonKey(name: 'isSave')
  String? isSave;

  @JsonKey(name: 'isNecessary')
  String? isNecessary;

  @JsonKey(name: 'couponPrice')
  String? couponPrice;

  BillDetailInfo(
    this.bookkeepingId,
    this.bookkeepingNumber,
    this.bookkeepingName,
    this.id,
    this.icon,
    this.iconImage,
    this.categoryId,
    this.money,
    this.after,
    this.memo,
    this.type,
    this.date,
    this.week,
    this.categoryName,
    this.accountName,
    this.accountId,
    this.accountType,
    this.cardNo,
    this.isNecessaryStatus,
    this.isSave,
    this.isNecessary,
    this.couponPrice,
  );

  factory BillDetailInfo.fromJson(Map<String, dynamic> srcJson) => _$BillDetailInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BillDetailInfoToJson(this);
}

@JsonSerializable()
class BookkeepingDetailResp extends Object {
  @JsonKey(name: 'bookkeepingInfo')
  BookkeepingInfo? bookkeepingInfo;

  @JsonKey(name: 'monthExpenditure')
  String? monthExpenditure;

  @JsonKey(name: 'monthIncome')
  String? monthIncome;

  @JsonKey(name: 'monthBalance')
  String? monthBalance;

  @JsonKey(name: 'flowingWaterLog')
  List<BookkeepingDateLog>? flowingWaterLog;

  BookkeepingDetailResp(
    this.bookkeepingInfo,
    this.monthExpenditure,
    this.monthIncome,
    this.monthBalance,
    this.flowingWaterLog,
  );

  factory BookkeepingDetailResp.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingDetailRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingDetailRespToJson(this);
}

@JsonSerializable()
class BookkeepingDateLog extends Object {
  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'weekChinese')
  String? weekChinese;

  @JsonKey(name: 'income')
  String? income;

  @JsonKey(name: 'expense')
  String? expense;

  @JsonKey(name: 'total')
  String? total;

  @JsonKey(name: 'items')
  List<BookkeepingLogItem>? items;

  BookkeepingDateLog(
    this.date,
    this.weekChinese,
    this.income,
    this.expense,
    this.total,
    this.items,
  );

  factory BookkeepingDateLog.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingDateLogFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingDateLogToJson(this);
}

@JsonSerializable()
class BookkeepingLogItem extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'categoryName')
  String? categoryName;

  @JsonKey(name: 'categoryIcon')
  String? categoryIcon;

  @JsonKey(name: 'money')
  String? money;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'type')
  String? type; // 1=收入 2=支出

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'time')
  String? time;

  BookkeepingLogItem(
    this.id,
    this.categoryName,
    this.categoryIcon,
    this.money,
    this.memo,
    this.type,
    this.accountName,
    this.time,
  );

  factory BookkeepingLogItem.fromJson(Map<String, dynamic> srcJson) => _$BookkeepingLogItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BookkeepingLogItemToJson(this);
}
