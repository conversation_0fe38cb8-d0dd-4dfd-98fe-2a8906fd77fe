import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qiazhun/main.dart';
import 'package:qiazhun/modules/account/account_add_page.dart';
import 'package:qiazhun/modules/account/account_detail_page.dart';
import 'package:qiazhun/modules/account/account_edit_page.dart';
import 'package:qiazhun/modules/account/account_list_page.dart';
import 'package:qiazhun/modules/account/repayment_page.dart';
import 'package:qiazhun/modules/account/transfer_page.dart';
import 'package:qiazhun/modules/auth/login_page.dart';
import 'package:qiazhun/modules/bank_list/bank_list_page.dart';
import 'package:qiazhun/modules/bookkeeping/debt_page.dart';
import 'package:qiazhun/modules/category/category_list_page.dart';
import 'package:qiazhun/modules/category/category_setting_page.dart';
import 'package:qiazhun/modules/home/<USER>';
import 'package:qiazhun/modules/membership/membership2_page.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/modules/auth/verification_page.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page.dart';
import 'package:qiazhun/modules/budget/budget_setting_page.dart';
import 'package:qiazhun/modules/calendar/calendar_page.dart';
import 'package:qiazhun/modules/category/add_category_page.dart';
import 'package:qiazhun/modules/bookkeeping/add_bookkeeping_page.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_list_page.dart';
import 'package:qiazhun/modules/membership/membership_page.dart';
import 'package:qiazhun/modules/mine_tab/mine_tab_page.dart';
import 'package:qiazhun/modules/detail_tab/detail_tab_page.dart';
import 'package:qiazhun/modules/my_assets/my_assets_page.dart';
import 'package:qiazhun/modules/plan_tab/add_saving_dialog.dart';
import 'package:qiazhun/modules/plan_tab/plan_detail_page.dart';
import 'package:qiazhun/modules/plan_tab/plan_edit_page.dart';
import 'package:qiazhun/modules/plan_tab/plan_page.dart';
import 'package:qiazhun/modules/plan_tab/plan_tab_page.dart';
import 'package:qiazhun/modules/search/search_page.dart';
import 'package:qiazhun/modules/settings/app_setting_page.dart';
import 'package:qiazhun/modules/settings/setting_model.dart';
import 'package:qiazhun/modules/stats_tab/income_outcome_detail_page.dart';
import 'package:qiazhun/modules/stats_tab/stat_tab_page.dart';
import 'package:qiazhun/modules/stats_tab/statistics_page.dart';
import 'package:qiazhun/pages/about_page.dart';
import 'package:qiazhun/pages/annual_fee_plan_page.dart';
import 'package:qiazhun/pages/consumption_guide_page.dart';
import 'package:qiazhun/pages/feedback_page.dart';
import 'package:qiazhun/pages/qa_page.dart';
import 'package:qiazhun/pages/repay_diary_page.dart';
import 'package:qiazhun/pages/saving_page.dart';
import 'package:qiazhun/pages/splash_page.dart';
import 'package:qiazhun/pages/unnecessary_page.dart';
import 'package:qiazhun/pages/category_water_flow_page.dart';
import 'package:qiazhun/pages/web_page.dart';
import 'package:qiazhun/router/app_router_observer.dart';
import 'package:qiazhun/router/dialog_page.dart';
import 'package:qiazhun/widgets/choose_ledgar_panel.dart';
import 'package:qiazhun/widgets/custom_popup_panel.dart';
import 'package:qiazhun/widgets/icon_list_panel.dart';

import '../modules/home/<USER>';

class Routes {
  static const indexPath = '/';
  static const detailTabPath = '/detail';
  static const statTabPath = '/stat';
  static const planTabPath = '/plan';
  static const mineTabPath = '/mine';
  static const calendarPath = '/calendar';
  static const planDetailPath = '/plan-detail';
  static const planEditPath = '/plan-edit';
  static const ledgarListPath = '/ledgar-list';
  static const addLedgerPath = '/ledgar-add';
  static const chooseLedgarPath = '/ledgar-choose';
  static const addCategoryPath = '/category-add';
  static const settingCategoryPath = '/category-setting';
  static const categoryListPath = '/category-list';

  static const incomeOutcomeDetailPath = '/income-outcome-detail';

  static const moneyFlowPath = '/money-flow';
  static const categoryWaterFlowPath = '/category-water-flow';

  static const myAssetsPath = '/myassets';
  static const feedbackPath = '/feedback';
  static const addAccountPath = '/account-add';
  static const editAccountPath = '/account-edit';
  static const searchPath = '/search';
  static const savingPath = '/saving';
  static const qaPath = '/qa';
  static const aboutPath = '/about';
  static const unnecessaryPath = '/unnecessary';
  static const annualFeePlanPath = '/annual-fee-plan';
  static const repayDiaryPath = '/repay-diary';
  static const consumptionGuidePath = '/consumption-guide';

  static const accountListPath = '/account-list';
  static const accountDetailPath = '/account-detail';
  static const bankListPath = '/bank-list';
  static const repaymentPath = '/repayment';
  static const transferPath = '/transfer';

  static const addSavingDialogPath = '/add-saving';
  static const repayBillDialogPath = '/repay-bill';
  static const iconListPath = '/icon-list';
  static const customPopupPath = '/custom-popup';
  static const popupPath = '/popup';
  static const membershipPath = '/membership';
  static const membership2Path = '/membership2';
  static const appSettingPath = '/appsetting';
  static const bookkeepingPath = '/bookkeeping';

  static const budgetSettingPath = '/budget-setting';

  static const loginPath = '/login';
  static const verificationPath = '/verification';
  static const webPath = '/web';
  static const adPath = '/ad';

  static const debtPath = '/debt';
}

class RouterHelper {
  static final RouterHelper _instance = RouterHelper._internal();

  static RouterHelper get instance => _instance;

  static late final GoRouter router;

  static RouteObserver<PageRoute> routeObserver = AppRouteObserver();

  static final GlobalKey<NavigatorState> _sectionANavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'sectionANav');
  static final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'rootNav');

  static final GlobalKey<NavigatorState> parentNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> homeTabNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> searchTabNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> settingsTabNavigatorKey = GlobalKey<NavigatorState>();

  BuildContext get context => router.routerDelegate.navigatorKey.currentContext!;

  GoRouterDelegate get routerDelegate => router.routerDelegate;

  GoRouteInformationParser get routeInformationParser => router.routeInformationParser;

  // static const String signUpPath = '/signUp';
  // static const String signInPath = '/signIn';
  // static const String detailPath = '/detail';
  // static const String rootDetailPath = '/rootDetail';

  // static const String homePath = '/home';
  // static const String settingsPath = '/settings';
  // static const String searchPath = '/search';

  factory RouterHelper() {
    return _instance;
  }

  RouterHelper._internal() {
    final routes = [
      StatefulShellRoute.indexedStack(
        parentNavigatorKey: parentNavigatorKey,
        branches: [
          StatefulShellBranch(
            navigatorKey: _sectionANavigatorKey,
            routes: <RouteBase>[
              GoRoute(
                path: Routes.detailTabPath,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return getPage(
                    child: const DetailTabPage(),
                    state: state,
                  );
                },
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: Routes.statTabPath,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return getPage(
                    child: const StatisticsTabPage(),
                    state: state,
                  );
                },
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: Routes.planTabPath,
                builder: (BuildContext context, GoRouterState state) => const PlanTabPage(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: Routes.mineTabPath,
                builder: (BuildContext context, GoRouterState state) => const MyProfilePage(),
              ),
            ],
          ),
        ],
        pageBuilder: (
          BuildContext context,
          GoRouterState state,
          StatefulNavigationShell navigationShell,
        ) {
          return getPage(
            child: TabbarPage(
              navigationShell,
            ),
            state: state,
          );
        },
      ),
      // GoRoute(
      //   parentNavigatorKey: parentNavigatorKey,
      //   path: Routes.INDEX,
      //   builder: (context, state) => const TabbarPage(),
      // ),
      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.loginPath,
        name: Routes.loginPath,
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
          parentNavigatorKey: parentNavigatorKey,
          path: Routes.verificationPath,
          name: Routes.verificationPath,
          builder: (context, state) {
            int bindType = (state.extra as Map<String, dynamic>?)?['bindType'] ?? 0;
            return VerificationPage(
              bindType: bindType,
            );
          }),
      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.calendarPath,
        name: Routes.calendarPath,
        builder: (context, state) => const CalendarPage(),
      ),
      GoRoute(
        path: '${Routes.planDetailPath}/:planId',
        name: Routes.planDetailPath,
        pageBuilder: (context, state) {
          return getPage(child: PlanDetailPage(state.pathParameters['planId']!), state: state);
        },
      ),
      GoRoute(
        path: '${Routes.planEditPath}/:planId',
        name: Routes.planEditPath,
        pageBuilder: (context, state) {
          return getPage(child: PlanEditPage(planId: state.pathParameters['planId']!), state: state);
        },
      ),
      GoRoute(
        path: Routes.ledgarListPath,
        name: Routes.ledgarListPath,
        pageBuilder: (context, state) {
          return getPage(child: BookkeepingListPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.addLedgerPath,
        name: Routes.addLedgerPath,
        pageBuilder: (context, state) {
          dynamic bookkeepingNumber = (state.extra as Map<String, dynamic>?)?['bookkeepingNumber'];
          return getPage(child: AddBookkeepingPage(bookkeepingNumber: bookkeepingNumber), state: state);
        },
      ),
      GoRoute(
        path: Routes.accountListPath,
        name: Routes.accountListPath,
        pageBuilder: (context, state) {
          dynamic selectMode = (state.extra as Map<String, dynamic>?)?['selectMode'];
          dynamic limitAccount = (state.extra as Map<String, dynamic>?)?['limitAccount'];
          dynamic initialTab = (state.extra as Map<String, dynamic>?)?['initialTab'];
          dynamic initialSubTab = (state.extra as Map<String, dynamic>?)?['initialSubTab'];
          return getPage(
              child: AccountListPage(
                isSelectMode: selectMode == true,
                limitAccount: limitAccount,
                initialTab: initialTab,
                initialSubTab: initialSubTab,
              ),
              state: state);
        },
      ),
      GoRoute(
        path: Routes.accountDetailPath,
        name: Routes.accountDetailPath,
        pageBuilder: (context, state) {
          String accountId = (state.extra as Map<String, dynamic>?)?['accountId'] ?? '';
          return getPage(child: AccountDetailPage(accountId: accountId), state: state);
        },
      ),
      GoRoute(
        path: Routes.bankListPath,
        name: Routes.bankListPath,
        pageBuilder: (context, state) {
          dynamic selectMode = (state.extra as Map<String, dynamic>?)?['selectMode'];
          return getPage(child: BankListPage(isSelectMode: selectMode == true), state: state);
        },
      ),
      GoRoute(
        path: Routes.repaymentPath,
        name: Routes.repaymentPath,
        pageBuilder: (context, state) {
          String accountId = (state.extra as Map<String, dynamic>?)?['accountId'] ?? '';
          return getPage(child: RepaymentPage(accountId: accountId), state: state);
        },
      ),
      GoRoute(
        path: Routes.transferPath,
        name: Routes.transferPath,
        pageBuilder: (context, state) {
          String fromAccountId = (state.extra as Map<String, dynamic>?)?['fromAccountId'] ?? '';
          return getPage(child: TransferPage(fromAccountId: fromAccountId), state: state);
        },
      ),
      GoRoute(
        path: '${Routes.addSavingDialogPath}/:planId',
        name: Routes.addSavingDialogPath,
        pageBuilder: (context, state) {
          return DialogPage(builder: (_) => AddSavingDialog(state.pathParameters['planId']!));
        },
      ),
      GoRoute(
        path: Routes.repayBillDialogPath,
        name: Routes.repayBillDialogPath,
        pageBuilder: (context, state) {
          return DialogPage(builder: (_) => RepayBillDialog());
        },
      ),
      GoRoute(
        path: Routes.budgetSettingPath,
        name: Routes.budgetSettingPath,
        pageBuilder: (context, state) {
          String? expandType = (state.extra as Map<String, String>?)?['expand'];
          return getPage(child: BudgetSettingPage(expandType), state: state);
        },
      ),
      GoRoute(
        path: Routes.bookkeepingPath,
        name: Routes.bookkeepingPath,
        pageBuilder: (context, state) {
          dynamic logId = state.uri.queryParameters['logId'];
          return getPage(child: BookkeepingPage(logId: logId), state: state);
        },
      ),
      GoRoute(
        path: Routes.debtPath,
        name: Routes.debtPath,
        pageBuilder: (context, state) {
          return getPage(child: const DebtPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.membershipPath,
        name: Routes.membershipPath,
        pageBuilder: (context, state) {
          return getPage(child: const MembershipPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.membership2Path,
        name: Routes.membership2Path,
        pageBuilder: (context, state) {
          return getPage(child: const Membership2Page(), state: state);
        },
      ),
      GoRoute(
        path: Routes.appSettingPath,
        name: Routes.appSettingPath,
        pageBuilder: (context, state) {
          return getPage(child: const AppSettingPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.iconListPath,
        name: Routes.iconListPath,
        pageBuilder: (context, state) {
          List<IconInfo> iconList = (state.extra as Map<String, dynamic>)['iconList'];
          return ModalPage(child: IconListPanel(iconList));
        },
      ),
      GoRoute(
        path: Routes.customPopupPath,
        name: Routes.customPopupPath,
        pageBuilder: (context, state) {
          String title = (state.extra as Map)['title'];
          Widget? widget = (state.extra as Map)['widget'];
          dynamic onConfirm = (state.extra as Map)['onConfirm'];
          return ModalPage(
              child: CustomPopupPanel(
            title: title,
            widget: widget,
            onConfirm: onConfirm,
          ));
        },
      ),
      GoRoute(
        path: Routes.popupPath,
        name: Routes.popupPath,
        pageBuilder: (context, state) {
          AppBar? appBar = (state.extra as Map)['appBar'];
          Widget widget = (state.extra as Map)['widget'];
          List<Widget>? buttons = (state.extra as Map)['buttons'];
          return DialogPage(builder: (_) => PopupPanel(widget, appBar: appBar, footerButtons: buttons));
        },
      ),
      GoRoute(
        path: Routes.chooseLedgarPath,
        name: Routes.chooseLedgarPath,
        pageBuilder: (context, state) {
          List<String>? selected = (state.extra as Map<String, dynamic>)['selected'];
          bool? multiSelect = (state.extra as Map<String, dynamic>)['multiSelect'];
          bool? createNew = (state.extra as Map<String, dynamic>)['createNew'];
          return ModalPage(child: ChooseLedgarPanel(selected, multiSelect, createNew));
        },
      ),
      GoRoute(
        path: Routes.addCategoryPath,
        name: Routes.addCategoryPath,
        pageBuilder: (context, state) {
          return getPage(child: AddCategoryPage(), state: state);
        },
      ),
      GoRoute(
        path: '${Routes.categoryListPath}/:categoryType',
        name: Routes.categoryListPath,
        pageBuilder: (context, state) {
          return getPage(child: CategoryListPage(state.pathParameters['categoryType']!), state: state);
        },
      ),
      GoRoute(
        path: Routes.settingCategoryPath,
        name: Routes.settingCategoryPath,
        pageBuilder: (context, state) {
          int tabId = (state.extra as Map<String, dynamic>?)?['tab_id'] ?? 0;
          return getPage(
              child: CategorySettingPage(
                initialTab: tabId,
              ),
              state: state);
        },
      ),
      GoRoute(
        path: Routes.myAssetsPath,
        name: Routes.myAssetsPath,
        pageBuilder: (context, state) {
          return getPage(child: MyAssetsPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.feedbackPath,
        name: Routes.feedbackPath,
        pageBuilder: (context, state) {
          return getPage(child: FeedbackPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.searchPath,
        name: Routes.searchPath,
        pageBuilder: (context, state) {
          return getPage(child: const SearchPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.qaPath,
        name: Routes.qaPath,
        pageBuilder: (context, state) {
          return getPage(child: const QaPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.aboutPath,
        name: Routes.aboutPath,
        pageBuilder: (context, state) {
          return getPage(child: const AboutPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.savingPath,
        name: Routes.savingPath,
        pageBuilder: (context, state) {
          int pageType = (state.extra as Map?)?['page_type'] ?? 0;
          return getPage(child: SavingPage(pageType: pageType), state: state);
        },
      ),
      GoRoute(
        path: Routes.annualFeePlanPath,
        name: Routes.annualFeePlanPath,
        pageBuilder: (context, state) {
          return getPage(child: const AnnualFeePlanPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.consumptionGuidePath,
        name: Routes.consumptionGuidePath,
        pageBuilder: (context, state) {
          return getPage(child: const ConsumptionGuidePath(), state: state);
        },
      ),
      GoRoute(
        path: Routes.repayDiaryPath,
        name: Routes.repayDiaryPath,
        pageBuilder: (context, state) {
          return getPage(child: const RepayDiaryPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.addAccountPath,
        name: Routes.addAccountPath,
        pageBuilder: (context, state) {
          dynamic accountId = (state.extra as Map<String, dynamic>?)?['accountId'];
          dynamic initialTab = (state.extra as Map<String, dynamic>?)?['initialTab'];
          dynamic initialSubTab = (state.extra as Map<String, dynamic>?)?['initialSubTab'];
          return getPage(
              child: AccountAddPage(
                accountId: accountId,
                initialTab: initialTab,
                initialSubTab: initialSubTab,
              ),
              state: state);
        },
      ),
      GoRoute(
        path: Routes.moneyFlowPath,
        name: Routes.moneyFlowPath,
        pageBuilder: (context, state) {
          int pageType = (state.extra as Map?)?['page_type'] ?? 1;
          String timeRangeType = (state.extra as Map?)?['time_range_type'] ?? '';
          return getPage(child: MoneyFlowPage(pageType: pageType, timeRangeType: timeRangeType), state: state);
        },
      ),
      GoRoute(
        path: Routes.categoryWaterFlowPath,
        name: Routes.categoryWaterFlowPath,
        pageBuilder: (context, state) {
          int pageType = (state.extra as Map?)?['page_type'] ?? 1;
          int dateRangeType = (state.extra as Map?)?['date_range_type'] ?? 1;
          DateTime dateBegin = (state.extra as Map?)?['date_range_begin'] ?? DateTime.now();
          DateTime dateEnd = (state.extra as Map?)?['date_range_end'] ?? DateTime.now();
          String categoryId = (state.extra as Map?)?['categoryId'] ?? '';
          String title = (state.extra as Map?)?['title'] ?? '';
          return getPage(
              child: CategoryWaterFlowPage(
                title: title,
                pageType: pageType,
                dateRangeType: dateRangeType,
                dateBegin: dateBegin,
                dateEnd: dateEnd,
                categoryId: categoryId,
              ),
              state: state);
        },
      ),
      GoRoute(
        path: Routes.incomeOutcomeDetailPath,
        name: Routes.incomeOutcomeDetailPath,
        pageBuilder: (context, state) {
          return getPage(child: IncomeOutcomeDetailPage(), state: state);
        },
      ),
      GoRoute(
        path: Routes.editAccountPath,
        name: Routes.editAccountPath,
        pageBuilder: (context, state) {
          dynamic accountId = (state.extra as Map<String, dynamic>?)?['accountId'];
          dynamic initialTab = (state.extra as Map<String, dynamic>?)?['initialTab'];
          dynamic initialSubTab = (state.extra as Map<String, dynamic>?)?['initialSubTab'];
          return getPage(
              child: AccountEditPage(
                accountId: accountId,
                initialTab: initialTab,
                initialSubTab: initialSubTab,
              ),
              state: state);
        },
      ),
      GoRoute(
        path: Routes.webPath,
        name: Routes.webPath,
        pageBuilder: (context, state) {
          dynamic url = (state.extra as Map<String, dynamic>?)?['url'];
          String? title = (state.extra as Map<String, dynamic>?)?['title'];
          return getPage(child: WebPage(url, title: title), state: state);
        },
      ),
      GoRoute(
        path: Routes.adPath,
        name: Routes.adPath,
        pageBuilder: (context, state) {
          return getPage(child: const SplashPage(), state: state);
        },
      )
      // ModalBottomSheetRoute(name: )
    ];

    router = GoRouter(
      navigatorKey: parentNavigatorKey,
      initialLocation: Routes.detailTabPath,
      debugLogDiagnostics: true,
      routes: routes,
      redirect: (context, state) {
        if (UserStore.to.isUserLogin() || Routes.webPath == state.fullPath) {
          return null;
        } else if (Routes.loginPath != state.fullPath && Routes.verificationPath != state.fullPath) {
          return Routes.loginPath;
        }
        return null;
      },
    );
  }

  static Page getPage({
    required Widget child,
    required GoRouterState state,
  }) {
    return MaterialPage(
      key: state.pageKey,
      child: child,
    );
  }
}

class ModalPage<T> extends Page<T> {
  const ModalPage({required this.child});

  final Widget child;

  @override
  Route<T> createRoute(BuildContext context) => ModalBottomSheetRoute<T>(
      settings: this,
      builder: (context) => DraggableScrollableSheet(
            builder: (context, scroller) {
              return child;
            },
            expand: false,
          ),
      isScrollControlled: true,
      showDragHandle: false);
}
