class PriceInfo {
  String originPrice;
  bool isNegative;
  List<String> priceParts;

  PriceInfo(this.originPrice, this.isNegative, this.priceParts);

  static PriceInfo parsePrice(String price) {
    double dPrice = double.tryParse(price) ?? 0.0;
    _parseDouble(dPrice);
    return PriceInfo(price, dPrice.isNegative, _parseDouble(dPrice));
  }

  static List<String> _parseDouble(double value) {
    String sign = value.isNegative ? "-" : "+";
    double absValue = value.abs();

    String valueStr = absValue.toStringAsFixed(2);
    List<String> parts = valueStr.split('.');

    String integerPart = parts[0];
    String fractionalPart = ".00"; // Default if no fractional part

    if (parts.length > 1) {
      fractionalPart = '.${parts[1]}';
    }

    return [sign, integerPart, fractionalPart];
  }

  String get integerPart {
    if (isNegative) {
      return '-${priceParts[1]}';
    }
    return priceParts[1];
  }
}
